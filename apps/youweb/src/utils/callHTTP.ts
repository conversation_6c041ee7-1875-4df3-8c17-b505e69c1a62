import { toast } from '@repo/ui/components/ui/sonner';
import * as Sentry from '@sentry/react';
import { isErrorInfo, type RestErrorInfo } from './error';

import { streamReceiver } from './streamReceiver';

const isPlainObject = (obj: unknown) =>
  obj !== null && typeof obj === 'object' && Object.getPrototypeOf(obj) === Object.prototype;

type FetchOptionsWithoutBody = Omit<NonNullable<Parameters<typeof fetch>[1]>, 'body'>;

const handleFetchOptions = (
  options: {
    silent?: boolean;
    noRedirect?: boolean;
    body?: Record<string, unknown> | BodyInit | unknown[];
  } & FetchOptionsWithoutBody,
) => {
  const { body, ...restOptions } = options;
  const headers = new Headers(restOptions.headers);
  // 默认使用 application/json
  if (!headers.has('Content-Type')) {
    headers.set('Content-Type', 'application/json');
  }

  // 如果 body 是纯对象，并且 content-type 是 application/json，将 body 转换为 json 字符串
  const fetchBody =
    (isPlainObject(body) || Array.isArray(body)) &&
    headers.get('Content-Type')?.startsWith('application/json')
      ? JSON.stringify(body)
      : body;

  return { ...restOptions, headers, body: fetchBody as BodyInit };
};

async function handleErrorResponse(
  response: Response,
  options: {
    silent?: boolean;
    isInternalHTTPCall: boolean;
    noRedirect?: boolean;
  },
): Promise<{ error: RestErrorInfo }> {
  const contentType = response.headers.get('Content-Type');
  const isInternalHTTPCall = options.isInternalHTTPCall;

  // 处理内部 API 调用
  if (isInternalHTTPCall) {
    if (contentType?.startsWith('application/json')) {
      const json = await response.json();
      if (isErrorInfo(json)) {
        if (json.status === 401 && !options.noRedirect) {
          // 跳转到登录页
          window.location.href = '/sign-in';
          return { error: json };
        } else if (json.status === 404 && !options.noRedirect) {
          // TODO 所有请求 404 都要去 404 页面吗，如果是一些 delete 之类的感觉不用
          // window.location.href = '/404';
          return { error: json };
        } else {
          if (!options.silent) {
            toast('Oops, something went wrong.', {
              description: `${json.code}: ${json.message}`,
              duration: 5000,
            });
          }
          // !client 的情况，不做处理
        }
      } else {
        Sentry.captureException(json);
      }
      console.error('callHTTP error:', json);
      return { error: json };
    }

    const text = await response.text();
    const isTimeout = text.includes('FUNCTION_INVOCATION_TIMEOUT');
    const errorInfo = {
      status: isTimeout ? 504 : response.status,
      code: isTimeout ? 'FunctionInvocationTimeout' : response.statusText,
      message: isTimeout ? 'Function invocation timeout.' : text,
    } satisfies RestErrorInfo;
    if (!options.silent) {
      toast('Oops, something went wrong.', {
        description: `${errorInfo.code}: ${errorInfo.message}`,
        duration: 5000,
      });
    }
    Sentry.captureException(errorInfo);
    console.error('callHTTP error:', text);
    return { error: errorInfo };
  }

  // 处理外部 API 调用
  if (!options.silent) {
    toast('Oops, something went wrong.', {
      duration: 10000,
    });
  }

  const errorInfo: RestErrorInfo = {
    status: response.status,
    code: response.statusText,
    message: response.statusText,
  };

  if (contentType?.startsWith('application/json')) {
    const json = await response.json();
    errorInfo.detail = json;
    console.error('callHTTP error:', json);
    Sentry.captureException(errorInfo);
    return { error: errorInfo };
  }

  const text = await response.text();
  errorInfo.detail = text;
  console.error('callHTTP error:', text);
  Sentry.captureException(errorInfo);
  return { error: errorInfo };
}

export type HTTPResponse<T> =
  | { data: T; error?: undefined }
  | { error: RestErrorInfo; data?: undefined };

/**
 * 调用 HTTP 请求, 可用于内部或者外部请求
 * @param url 请求地址
 * @param options 请求选项
 * @returns 返回结果为  { data: T, error?: RestErrorInfo }，
 * 如果是外部请求，error.detail中包含了解析后的 body，可以自行提取并针对处理
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function callHTTP<T = any>(
  url: string,
  options: {
    silent?: boolean;
    noRedirect?: boolean;
    body?: Record<string, unknown> | BodyInit | unknown[];
  } & FetchOptionsWithoutBody = {},
): Promise<HTTPResponse<T>> {
  try {
    const isInternalHTTPCall = url.startsWith('/api/');

    const handledOptions = handleFetchOptions(options);
    const response = await fetch(url, handledOptions);

    if (!response.ok) {
      return handleErrorResponse(response, {
        silent: options.silent,
        isInternalHTTPCall,
        noRedirect: options.noRedirect,
      });
    }

    // 成功的响应
    const contentType = response.headers.get('Content-Type');
    if (contentType?.startsWith('application/json')) {
      const data = await response.json();
      return { data };
    } else {
      const data = await response.text();
      return { data } as HTTPResponse<T>;
    }
  } catch (error) {
    // 处理网络错误等其他异常
    const errorInfo = {
      status: 500,
      code: error instanceof Error ? error.name : 'NetworkError',
      message: error instanceof Error ? error.message : 'Network error',
    } satisfies RestErrorInfo;
    // 上报 Sentry
    if (error instanceof Error && error.name === 'AbortError') {
      // 不上报 AbortError 到 Sentry, 来自用户的手动取消
      console.error('Request aborted by user:', error.message);
      return { error: errorInfo };
    }
    Sentry.captureException(error);
    if (!options.silent) {
      toast('Oops, something went wrong.', {
        description: `${errorInfo.code}: ${errorInfo.message}`,
        duration: 10000,
      });
    }

    return { error: errorInfo } as HTTPResponse<T>;
  }
}

/**
 * 调用 HTTP 获得服务端流式响应，大多在与 AI 交互的场景中使用。
 * @param url 请求地址
 * @param options 请求选项
 * options.onMessage: (data: T) => void; 收到流式响应的每个内容时，会调用该回调函数
 * options.shouldContinueCheck: () => boolean; 是否继续接收流式响应，可用此函数控制是否继续接收响应，
 * 当然，如果是需要终止服务端响应，请使用 options.signal: AbortSignal 来 abort，这样服务端也能收到中断信息
 * @returns 返回结果为  { error?: RestErrorInfo }，
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function callHTTPStream<T = any>(
  url: string,
  options: {
    silent?: boolean;
    noRedirect?: boolean;
    body?: Record<string, unknown> | BodyInit;
    shouldContinueCheck?: () => boolean;
    onMessage: (data: T) => void;
  } & FetchOptionsWithoutBody,
): Promise<{ error?: RestErrorInfo }> {
  try {
    const handledOptions = handleFetchOptions(options);
    if (!options.shouldContinueCheck) {
      options.shouldContinueCheck = () => true;
    }
    const response = await fetch(url, handledOptions);

    if (!response.ok) {
      const text = await response.text();
      try {
        const json = JSON.parse(text);
        if (isErrorInfo(json) && !options.silent) {
          toast('Oops, something went wrong.', {
            description: `${json.code}: ${json.message}`,
            duration: 10000,
          });
          console.error('callHTTPStream error:', json);

          return { error: json };
        }
      } catch (error) {
        // 被 catch 说明 errorInfo 不是 json 格式，而是纯 text;
        // 走下面的处理
        console.error('http stream error:', error);
      }
      const isTimeout = text.includes('FUNCTION_INVOCATION_TIMEOUT');
      const errorInfo = {
        status: isTimeout ? 504 : 500,
        code: isTimeout ? 'FunctionInvocationTimeout' : 'UnknownError',
        message: isTimeout ? 'Function invocation timeout.' : text,
        detail: text,
      } satisfies RestErrorInfo;

      if (!options.silent) {
        toast('Oops, something went wrong.', {
          description: `${errorInfo.code}: ${errorInfo.message}`,
          duration: 10000,
        });
      }
      console.error('callHTTPStream error:', errorInfo);
      return { error: errorInfo };
    }

    const reader = response.body!.getReader();
    reader.closed.catch((err) => {
      console.error('stream closed with error', err);
    });
    await streamReceiver<T>(reader, options.shouldContinueCheck, options.onMessage);

    return { error: undefined };
  } catch (error) {
    // 处理网络错误等其他异常
    const errorInfo = isErrorInfo(error)
      ? error
      : ({
          status: 500,
          code: error instanceof Error ? error.name : 'NetworkError',
          message: error instanceof Error ? error.message : 'Network error',
        } satisfies RestErrorInfo);

    if (error instanceof Error && error.name === 'AbortError') {
      // 不上报 AbortError 到 Sentry, 来自用户的手动取消
      console.error('Request aborted by user:', error.message);
      return { error: errorInfo };
    }

    console.error('callHTTPStream error:', error);
    if (!options.silent) {
      toast('Oops, something went wrong.', {
        description: `${errorInfo.code}: ${errorInfo.message}`,
        duration: 10000,
      });
    }
    return { error: errorInfo } as HTTPResponse<T>;
  }
}
