import { createSnakeCaseApiClient } from '@repo/api';
import { toast } from '@repo/ui/components/ui/sonner';
import * as Sentry from '@sentry/react';
import { isErrorInfo, type RestErrorInfo } from './error';

// 创建具有蛇形命名响应格式的 API 客户端
// 集成了 callHTTP 的错误处理、toast 通知和 Sentry 上报功能
export const apiClient = createSnakeCaseApiClient({
  // youweb 使用相对路径，通过代理或 Pages Functions 路由到后端
  // 开发环境: /api/* -> 代理到 localhost:3001 或 youmind-preview.vercel.app
  // 生产环境: /api/* -> 通过 Cloudflare Pages Functions 路由
  basePath: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
  // 默认启用 credentials，与 callHTTP 的行为保持一致
  useCredentials: true,
});

// 创建带有错误处理包装的 API 客户端
// 集成了 callHTTP 的错误处理逻辑：toast 通知、Sentry 上报、重定向等
export const createApiClientWithErrorHandling = (config: {
  accessToken?: string;
  headers?: Record<string, string>;
  basePath?: string;
  silent?: boolean;
  noRedirect?: boolean;
} = {}) => {
  const client = createSnakeCaseApiClient({
    basePath: config.basePath || '/api',
    headers: {
      'Content-Type': 'application/json',
      ...config.headers,
    },
    accessToken: config.accessToken,
    useCredentials: true,
  });

  // 包装所有 API 方法以添加错误处理
  const wrapApiWithErrorHandling = <T extends Record<string, any>>(api: T): T => {
    const wrappedApi = {} as T;

    for (const [key, method] of Object.entries(api)) {
      if (typeof method === 'function') {
        wrappedApi[key as keyof T] = (async (...args: any[]) => {
          try {
            return await method.apply(api, args);
          } catch (error: any) {
            // 处理错误，类似 callHTTP 的错误处理逻辑
            await handleApiError(error, {
              silent: config.silent,
              noRedirect: config.noRedirect,
            });
            throw error;
          }
        }) as T[keyof T];
      } else {
        wrappedApi[key as keyof T] = method;
      }
    }

    return wrappedApi;
  };

  // 包装常用的 API 客户端
  const wrappedClient = {
    ...client,
    userApi: wrapApiWithErrorHandling(client.userApi),
    authApi: wrapApiWithErrorHandling(client.authApi),
    chatV1Api: wrapApiWithErrorHandling(client.chatV1Api),
    chatV2Api: wrapApiWithErrorHandling(client.chatV2Api),
    searchApi: wrapApiWithErrorHandling(client.searchApi),
    // 可以根据需要添加更多 API 的包装
  };

  return wrappedClient;
};

// 错误处理函数，从 callHTTP 迁移过来的逻辑
async function handleApiError(
  error: any,
  options: {
    silent?: boolean;
    noRedirect?: boolean;
  } = {}
) {
  // 检查是否是网络错误
  if (error.name === 'AbortError') {
    console.error('Request aborted by user:', error.message);
    return;
  }

  // 检查是否是 API 响应错误
  if (error.response) {
    const response = error.response;
    const status = response.status;

    // 处理 401 未授权
    if (status === 401 && !options.noRedirect) {
      window.location.href = '/sign-in';
      return;
    }

    // 处理 404 错误（可选择是否跳转）
    if (status === 404 && !options.noRedirect) {
      // TODO: 根据业务需求决定是否跳转到 404 页面
      // window.location.href = '/404';
    }

    // 尝试解析错误信息
    let errorInfo: RestErrorInfo;
    try {
      const errorData = await response.json();
      if (isErrorInfo(errorData)) {
        errorInfo = errorData;
      } else {
        errorInfo = {
          status: response.status,
          code: response.statusText,
          message: response.statusText,
          detail: errorData,
        };
      }
    } catch {
      const text = await response.text();
      const isTimeout = text.includes('FUNCTION_INVOCATION_TIMEOUT');
      errorInfo = {
        status: isTimeout ? 504 : response.status,
        code: isTimeout ? 'FunctionInvocationTimeout' : response.statusText,
        message: isTimeout ? 'Function invocation timeout.' : text,
      };
    }

    // 显示错误提示
    if (!options.silent) {
      toast('Oops, something went wrong.', {
        description: `${errorInfo.code}: ${errorInfo.message}`,
        duration: 5000,
      });
    }

    // 上报到 Sentry
    Sentry.captureException(errorInfo);
    console.error('API error:', errorInfo);
  } else {
    // 处理其他类型的错误
    const errorInfo = {
      status: 500,
      code: error.name || 'NetworkError',
      message: error.message || 'Network error',
    } satisfies RestErrorInfo;

    if (!options.silent) {
      toast('Oops, something went wrong.', {
        description: `${errorInfo.code}: ${errorInfo.message}`,
        duration: 10000,
      });
    }

    Sentry.captureException(error);
    console.error('API error:', error);
  }
}

// 便利函数：创建带有静默错误处理的客户端
export const createSilentApiClient = (config: {
  accessToken?: string;
  headers?: Record<string, string>;
  basePath?: string;
} = {}) => {
  return createApiClientWithErrorHandling({
    ...config,
    silent: true,
  });
};

// 便利函数：创建不重定向的客户端（用于某些特殊场景）
export const createNoRedirectApiClient = (config: {
  accessToken?: string;
  headers?: Record<string, string>;
  basePath?: string;
} = {}) => {
  return createApiClientWithErrorHandling({
    ...config,
    noRedirect: true,
  });
};

// 类型定义：API 响应类型，类似 callHTTP 的 HTTPResponse
export type ApiResponse<T> =
  | { data: T; error?: undefined }
  | { error: RestErrorInfo; data?: undefined };

// 便利函数：包装 API 调用以返回类似 callHTTP 的响应格式
export const safeApiCall = async <T>(
  apiCall: () => Promise<T>,
  options: {
    silent?: boolean;
    noRedirect?: boolean;
  } = {}
): Promise<ApiResponse<T>> => {
  try {
    const data = await apiCall();
    return { data };
  } catch (error: any) {
    await handleApiError(error, options);

    // 构造错误信息
    let errorInfo: RestErrorInfo;
    if (error.response) {
      try {
        const errorData = await error.response.json();
        errorInfo = isErrorInfo(errorData) ? errorData : {
          status: error.response.status,
          code: error.response.statusText,
          message: error.response.statusText,
          detail: errorData,
        };
      } catch {
        const text = await error.response.text();
        const isTimeout = text.includes('FUNCTION_INVOCATION_TIMEOUT');
        errorInfo = {
          status: isTimeout ? 504 : error.response.status,
          code: isTimeout ? 'FunctionInvocationTimeout' : error.response.statusText,
          message: isTimeout ? 'Function invocation timeout.' : text,
        };
      }
    } else {
      errorInfo = {
        status: 500,
        code: error.name || 'NetworkError',
        message: error.message || 'Network error',
      };
    }

    return { error: errorInfo };
  }
};
