import { createCamelCaseApiClient, type ApiClientConfig } from '@repo/api';
import { toast } from '@repo/ui/components/ui/sonner';
import * as Sentry from '@sentry/react';

/**
 * youweb API 客户端配置
 *
 * youweb 使用代理配置，API 调用通过以下方式路由：
 * - 开发环境: /api/* -> 代理到 localhost:3001 或 youmind-preview.vercel.app
 * - 生产环境: /api/* -> 通过 Cloudflare Pages Functions 路由
 */

/**
 * 基础 API 客户端配置
 */
const createBaseApiClient = (config: ApiClientConfig = {}) => {
  return createCamelCaseApiClient({
    // youweb 使用相对路径，通过代理或 Pages Functions 路由到后端
    basePath: '/api',
    headers: {
      'Content-Type': 'application/json',
      ...config.headers,
    },
    // 在浏览器环境中使用 credentials: 'include' 来发送 cookies
    useCredentials: true,
    ...config,
  });
};

/**
 * 默认 API 客户端实例
 * 适用于大多数场景的基础客户端
 */
export const apiClient = createBaseApiClient();

/**
 * 创建带有错误处理的增强 API 客户端
 * 集成 youweb 现有的错误处理逻辑
 */
export const createEnhancedApiClient = (config: ApiClientConfig & {
  silent?: boolean;
  enableErrorToast?: boolean;
  enableSentryReporting?: boolean;
} = {}) => {
  const {
    silent = false,
    enableErrorToast = true,
    enableSentryReporting = true,
    ...apiConfig
  } = config;

  const baseClient = createBaseApiClient(apiConfig);

  // 创建错误处理包装器
  const wrapApiWithErrorHandling = <T extends Record<string, any>>(api: T): T => {
    const wrappedApi = {} as T;

    for (const [key, value] of Object.entries(api)) {
      if (typeof value === 'function') {
        wrappedApi[key as keyof T] = (async (...args: any[]) => {
          try {
            return await value.apply(api, args);
          } catch (error: any) {
            // 错误处理逻辑，与 callHTTP 保持一致
            if (!silent && enableErrorToast) {
              const errorMessage = error?.message || 'An unexpected error occurred';
              toast('Oops, something went wrong.', {
                description: errorMessage,
                duration: 5000,
              });
            }

            if (enableSentryReporting) {
              Sentry.captureException(error);
            }

            console.error(`API Error in ${key}:`, error);
            throw error;
          }
        }) as any;
      } else if (typeof value === 'object' && value !== null) {
        // 递归处理嵌套的 API 对象
        wrappedApi[key as keyof T] = wrapApiWithErrorHandling(value);
      } else {
        wrappedApi[key as keyof T] = value;
      }
    }

    return wrappedApi;
  };

  return wrapApiWithErrorHandling(baseClient);
};

/**
 * 带有请求日志的 API 客户端
 * 用于调试和监控
 */
export const createLoggingApiClient = (config: ApiClientConfig & {
  enableRequestLogging?: boolean;
  enableResponseLogging?: boolean;
} = {}) => {
  const {
    enableRequestLogging = true,
    enableResponseLogging = true,
    ...apiConfig
  } = config;

  const baseClient = createBaseApiClient(apiConfig);

  if (!enableRequestLogging && !enableResponseLogging) {
    return baseClient;
  }

  // 创建日志包装器
  const wrapApiWithLogging = <T extends Record<string, any>>(api: T, apiName: string): T => {
    const wrappedApi = {} as T;

    for (const [key, value] of Object.entries(api)) {
      if (typeof value === 'function') {
        wrappedApi[key as keyof T] = (async (...args: any[]) => {
          const startTime = Date.now();
          const requestId = `${apiName}.${key}-${Date.now()}`;

          if (enableRequestLogging) {
            console.log(`📤 [${requestId}] ${apiName}.${key}`, {
              args: args.length > 0 ? args : undefined,
              timestamp: new Date().toISOString(),
            });
          }

          try {
            const result = await value.apply(api, args);

            if (enableResponseLogging) {
              const responseTime = Date.now() - startTime;
              console.log(`📥 [${requestId}] Success (${responseTime}ms)`, {
                result: result ? 'Data received' : 'No data',
                responseTime,
              });
            }

            return result;
          } catch (error) {
            if (enableResponseLogging) {
              const responseTime = Date.now() - startTime;
              console.error(`❌ [${requestId}] Error (${responseTime}ms)`, {
                error: error instanceof Error ? error.message : error,
                responseTime,
              });
            }
            throw error;
          }
        }) as any;
      } else if (typeof value === 'object' && value !== null) {
        // 递归处理嵌套的 API 对象
        wrappedApi[key as keyof T] = wrapApiWithLogging(value, `${apiName}.${key}`);
      } else {
        wrappedApi[key as keyof T] = value;
      }
    }

    return wrappedApi;
  };

  return wrapApiWithLogging(baseClient, 'API');
};

/**
 * 生产环境推荐的 API 客户端
 * 包含错误处理但不包含详细日志
 */
export const productionApiClient = createEnhancedApiClient({
  enableErrorToast: true,
  enableSentryReporting: true,
});

/**
 * 开发环境推荐的 API 客户端
 * 包含错误处理和详细日志
 */
export const developmentApiClient = process.env.NODE_ENV === 'development'
  ? createLoggingApiClient({
      enableRequestLogging: true,
      enableResponseLogging: true,
    })
  : productionApiClient;

/**
 * 导出常用的 API 客户端实例
 * 根据环境自动选择合适的客户端
 */
export const recommendedApiClient = process.env.NODE_ENV === 'development'
  ? developmentApiClient
  : productionApiClient;



/**
 * 创建自定义配置的 API 客户端
 *
 * @example
 * ```typescript
 * // 创建静默客户端（不显示错误提示）
 * const silentClient = createCustomApiClient({
 *   silent: true,
 *   enableErrorToast: false
 * });
 *
 * // 创建带有自定义头部的客户端
 * const customClient = createCustomApiClient({
 *   headers: {
 *     'x-custom-header': 'custom-value',
 *     'x-request-source': 'youweb'
 *   }
 * });
 * ```
 */
export const createCustomApiClient = (config: ApiClientConfig & {
  silent?: boolean;
  enableErrorToast?: boolean;
  enableSentryReporting?: boolean;
  enableLogging?: boolean;
} = {}) => {
  const { enableLogging = false, ...restConfig } = config;

  if (enableLogging && process.env.NODE_ENV === 'development') {
    return createLoggingApiClient(restConfig);
  }

  return createEnhancedApiClient(restConfig);
};
