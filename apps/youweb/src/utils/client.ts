import { createSnakeCaseApiClient } from '@repo/api';

// 创建具有蛇形命名响应格式的 API 客户端
export const apiClient = createSnakeCaseApiClient({
  // youweb 使用相对路径，通过代理或 Pages Functions 路由到后端
  // 开发环境: /api/* -> 代理到 localhost:3001 或 youmind-preview.vercel.app
  // 生产环境: /api/* -> 通过 Cloudflare Pages Functions 路由
  basePath: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// 导出常用的 API 实例，方便直接使用
export const {
  userApi,
  authApi,
  boardApi,
  noteApi,
  thoughtApi,
  chatV1Api,
  chatV2Api,
  fileApi,
  searchApi,
  materialApi,
  aIApi,
  appApi,
  boardGroupApi,
  boardItemApi,
  chatShortcutApi,
  diffApi,
  entityApi,
  favoritesApi,
  healthApi,
  playlistItemApi,
  positionApi,
  shortLinkApi,
  snipApi,
  subscriptionApi,
  textApi,
  thoughtVersionApi,
  usageRecordApi,
  userPreferenceApi,
  webhookApi,
  // 可以根据需要添加更多 API
} = apiClient;

// 创建带有自定义配置的 API 客户端的辅助函数
export const createCustomApiClient = (config: {
  accessToken?: string;
  headers?: Record<string, string>;
  basePath?: string;
}) => {
  return createSnakeCaseApiClient({
    basePath: config.basePath || '/api',
    headers: {
      'Content-Type': 'application/json',
      ...config.headers,
    },
    accessToken: config.accessToken,
  });
};
