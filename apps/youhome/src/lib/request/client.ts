import { createCamelCaseApiClient } from '@repo/api';
// import { isLocalRuntime } from '@repo/common';
import { UserWithPreferenceVO } from '@/schema/userSchema';

// 创建具有蛇形命名响应格式的 API 客户端
export const apiClient = createCamelCaseApiClient({
  basePath:
    process.env.NODE_ENV === 'development'
      ? 'http://localhost:3001'
      : process.env.NEXT_PUBLIC_YOUMIND_API_BASE_PATH,
  headers: {
    'Content-Type': 'application/json',
  },
});

// export const apiClientSnake = createSnakeCaseApiClient({
//   basePath: isLocalRuntime() ? 'http://localhost:4000' : '/',
//   headers: {
//     'Content-Type': 'application/json',
//   },
// });

export async function tryGetCurrentUserFromClient() {
  try {
    const user = await apiClient.userApi.getCurrentUser();
    return user as UserWithPreferenceVO;
  } catch (error: any) {
    return null;
  }
}
