import { apiClient } from '../request/client';
import { notifyYouextUserSignOut } from '../youext/signOutNotify';

export async function logOut() {
  try {
    await apiClient.authApi.logOut();
    notifyYouextUserSignOut();
    window.location.replace('/');
  } catch (error) {
    // 静默处理错误，继续执行登出流程
    console.error('Logout error:', error);
    notifyYouextUserSignOut();
    window.location.replace('/');
  }
}
