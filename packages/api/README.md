# YouAPI 生成的客户端使用指南

本指南展示了如何使用生成的 TypeScript API 客户端，包含不同的头部配置。

## 🔧 **基本用法**

### **ApiClientConfig 配置选项**

```typescript
interface ApiClientConfig {
  basePath?: string;           // API 基础 URL (默认: http://localhost:4000)
  headers?: Record<string, string>;  // 自定义头部
  accessToken?: string;        // 使用独立 JWT 验证
  useCredentials?: boolean;    // 带上浏览器 Cookie，默认打开
}
```

### 1. **驼峰命名 API 客户端**

```typescript
import { createCamelCaseApiClient } from '@repo/api';

// 创建具有驼峰命名响应格式的 API 客户端
const apiClients = createCamelCaseApiClient({
  basePath: 'http://localhost:4000',
  headers: {
    'Content-Type': 'application/json',
    'x-custom-header': 'custom-value'
  }
});

// 使用 API 客户端
const user = await apiClients.userApi.getCurrentUser();
// 响应: { userId: "123", createdAt: "2024-01-01", ... }
```

### 2. **蛇形命名 API 客户端**

```typescript
import { createSnakeCaseApiClient } from '@repo/api';

// 创建具有蛇形命名响应格式的 API 客户端
const apiClients = createSnakeCaseApiClient({
  basePath: 'http://localhost:4000',
  headers: {
    'Content-Type': 'application/json'
  }
});

// API 响应将是蛇形命名格式
const user = await apiClients.userApi.getCurrentUser();
// 响应: { user_id: "123", created_at: "2024-01-01", ... }
```

## 🔧 **高级用法**

### **自定义头部**

```typescript
import { createCamelCaseApiClient } from '@repo/api';

// 创建具有动态头部的客户端
const createApiWithDynamicHeaders = (userId: string, sessionId: string) => {
  return createCamelCaseApiClient({
    basePath: 'http://localhost:4000',
    headers: {
      'Authorization': 'Bearer your-token',
      'x-user-id': userId,
      'x-session-id': sessionId,
      'x-timestamp': new Date().toISOString(),
      'x-request-id': `${userId}-${Date.now()}`
    }
  });
};

// 使用
const apiClients = createApiWithDynamicHeaders('user123', 'session456');
const user = await apiClients.userApi.getCurrentUser();
```

### **具有请求拦截器的预请求逻辑**

```typescript
import { createCamelCaseApiClient } from '@repo/api';

// 创建具有预请求逻辑的包装函数
const createApiWithPreRequestLogic = (basePath: string, token: string) => {
  const apiClients = createCamelCaseApiClient({
    basePath,
    headers: {
      'Authorization': `Bearer ${token}`,
      'x-app-version': '1.0.0'
    }
  });

  // 创建具有预请求逻辑的包装函数
  const userApiWithPreRequest = {
    getCurrentUser: async () => {
      // 预请求逻辑
      console.log('正在请求 getCurrentUser...');
      console.log('请求时间戳:', new Date().toISOString());

      // 添加请求特定的头部
      const requestHeaders = {
        'x-request-type': 'user-fetch',
        'x-cache-control': 'no-cache'
      };

      // 发出实际请求
      const result = await apiClients.userApi.getCurrentUser();

      // 后请求逻辑
      console.log('请求成功完成');
      return result;
    },

    updateUserName: async (data: { name: string }) => {
      // 预请求验证
      if (!data.name || data.name.trim().length === 0) {
        throw new Error('名称不能为空');
      }

      console.log('正在更新用户名为:', data.name);

      // 发出实际请求
      const result = await apiClients.userApi.updateUserName(data);

      console.log('用户名更新成功');
      return result;
    }
  };

  return {
    ...apiClients,
    userApi: userApiWithPreRequest
  };
};

// 使用
const apiClients = createApiWithPreRequestLogic('http://localhost:4000', 'your-token');
const user = await apiClients.userApi.getCurrentUser();
```

### **具有响应处理的后请求逻辑**

```typescript
import { createCamelCaseApiClient } from '@repo/api';

// 创建具有后请求逻辑的包装器
const createApiWithPostRequestLogic = (basePath: string) => {
  const apiClients = createCamelCaseApiClient({ basePath });

  // 跟踪请求指标
  const metrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0
  };

  const userApiWithMetrics = {
    getCurrentUser: async () => {
      const startTime = Date.now();
      metrics.totalRequests++;

      try {
        const result = await apiClients.userApi.getCurrentUser();

        // 后请求成功逻辑
        const responseTime = Date.now() - startTime;
        metrics.successfulRequests++;
        metrics.averageResponseTime =
          (metrics.averageResponseTime * (metrics.successfulRequests - 1) + responseTime) /
          metrics.successfulRequests;

        console.log(`✅ 请求在 ${responseTime}ms 内成功`);
        console.log('当前指标:', metrics);

        return result;
      } catch (error) {
        // 后请求错误逻辑
        metrics.failedRequests++;
        console.error('❌ 请求失败:', error);
        console.log('当前指标:', metrics);
        throw error;
      }
    }
  };

  return {
    ...apiClients,
    userApi: userApiWithMetrics,
    getMetrics: () => ({ ...metrics })
  };
};

// 使用
const apiClients = createApiWithPostRequestLogic('http://localhost:4000');
const user = await apiClients.userApi.getCurrentUser();
console.log('API 指标:', apiClients.getMetrics());
```

### **具有请求特定逻辑的自定义头部**

```typescript
import { createCamelCaseApiClient } from '@repo/api';

// 创建具有请求特定头部的客户端
const createApiWithRequestHeaders = (basePath: string) => {
  const apiClients = createCamelCaseApiClient({ basePath });

  const userApiWithCustomHeaders = {
    getCurrentUser: async (options?: {
      includeProfile?: boolean;
      cacheControl?: string;
      requestId?: string;
    }) => {
      // 基于选项构建自定义头部
      const customHeaders: Record<string, string> = {
        'x-request-type': 'user-fetch',
        'x-timestamp': new Date().toISOString()
      };

      if (options?.includeProfile) {
        customHeaders['x-include-profile'] = 'true';
      }

      if (options?.cacheControl) {
        customHeaders['x-cache-control'] = options.cacheControl;
      }

      if (options?.requestId) {
        customHeaders['x-request-id'] = options.requestId;
      }

      // 注意：生成的客户端不直接支持每个请求的头部
      // 这是您可能如何构建它的概念示例
      console.log('正在使用头部发出请求:', customHeaders);

      return await apiClients.userApi.getCurrentUser();
    },

    updateUserName: async (data: { name: string }, options?: {
      validateOnly?: boolean;
      requestId?: string;
    }) => {
      const customHeaders: Record<string, string> = {
        'x-request-type': 'user-update',
        'x-timestamp': new Date().toISOString()
      };

      if (options?.validateOnly) {
        customHeaders['x-validate-only'] = 'true';
      }

      if (options?.requestId) {
        customHeaders['x-request-id'] = options.requestId;
      }

      console.log('正在使用头部更新用户:', customHeaders);

      return await apiClients.userApi.updateUserName(data);
    }
  };

  return {
    ...apiClients,
    userApi: userApiWithCustomHeaders
  };
};

// 使用
const apiClients = createApiWithRequestHeaders('http://localhost:4000');

// 具有自定义选项的请求
const user = await apiClients.userApi.getCurrentUser({
  includeProfile: true,
  cacheControl: 'no-cache',
  requestId: 'req-123'
});

// 仅验证的更新
await apiClients.userApi.updateUserName(
  { name: '新名称' },
  { validateOnly: true, requestId: 'req-456' }
);
```

### **请求/响应日志记录和监控**

```typescript
import { createCamelCaseApiClient } from '@repo/api';

// 创建具有全面日志记录的客户端
const createApiWithLogging = (basePath: string) => {
  const apiClients = createCamelCaseApiClient({ basePath });

  const logRequest = (method: string, url: string, headers: Record<string, string>) => {
    console.log(`📤 ${method} ${url}`);
    console.log('头部:', headers);
  };

  const logResponse = (status: number, responseTime: number, data?: any) => {
    console.log(`📥 响应 ${status} (${responseTime}ms)`);
    if (data) {
      console.log('响应数据:', data);
    }
  };

  const userApiWithLogging = {
    getCurrentUser: async () => {
      const startTime = Date.now();
      const requestId = `req-${Date.now()}`;

      // 预请求日志记录
      logRequest('GET', '/api/user/getCurrentUser', {
        'x-request-id': requestId,
        'x-timestamp': new Date().toISOString()
      });

      try {
        const result = await apiClients.userApi.getCurrentUser();

        // 后请求成功日志记录
        const responseTime = Date.now() - startTime;
        logResponse(200, responseTime, result);

        return result;
      } catch (error: any) {
        // 后请求错误日志记录
        const responseTime = Date.now() - startTime;
        logResponse(error.response?.status || 500, responseTime);
        console.error('请求失败:', error.message);
        throw error;
      }
    }
  };

  return {
    ...apiClients,
    userApi: userApiWithLogging
  };
};

// 使用
const apiClients = createApiWithLogging('http://localhost:4000');
const user = await apiClients.userApi.getCurrentUser();
```

## 🔧 **错误处理**

### **基本错误处理**

```typescript
import { createCamelCaseApiClient } from '@repo/api';

const apiClients = createCamelCaseApiClient({
  basePath: 'http://localhost:4000',
  headers: {
    'Authorization': 'Bearer your-token'
  }
});

try {
  const user = await apiClients.userApi.getCurrentUser();
  console.log('用户:', user);
} catch (error) {
  if (error instanceof Error) {
    console.error('API 错误:', error.message);
  }
}
```
